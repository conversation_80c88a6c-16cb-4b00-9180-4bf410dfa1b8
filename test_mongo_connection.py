#!/usr/bin/env python3
"""
Test MongoDB Connection Script

This script demonstrates how to test MongoDB connections using the extracted details
from the API response.
"""

import json
import sys
from pymongo import MongoClient
from pymongo.errors import ServerSelectionTimeoutError, OperationFailure
import requests


def test_mongo_connection_from_api(tenant: str, dealer_id: str, auth_token: str = "ved"):
    """
    Test MongoDB connection using details extracted from API response.
    """
    api_url = f"http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com/config/v2/read/secondary/MONGO/DSE_DEFAULT/{tenant}/{dealer_id}"
    
    try:
        print("Step 1: Fetching configuration from API...")
        print(f"API URL: {api_url}")
        
        # Make API request
        response = requests.get(
            api_url,
            params={"authToken": auth_token},
            timeout=30
        )
        response.raise_for_status()
        
        config_data = response.json()
        print(f"✓ API response received")
        
        # Handle list response
        if isinstance(config_data, list):
            if len(config_data) > 0:
                config_data = config_data[0]
            else:
                raise ValueError("API returned empty list")
        
        print(f"Raw API Response: {json.dumps(config_data, indent=2)}")
        
        print("\nStep 2: Extracting MongoDB connection details...")
        
        # Extract connection details according to the specified structure
        secondary_host_configs = config_data.get('secondaryHostConfigs', {})
        hosts = secondary_host_configs.get('hosts', {})
        
        # Handle hosts - could be a dict or list
        if isinstance(hosts, list) and len(hosts) > 0:
            host_info = hosts[0]  # Use first host
        elif isinstance(hosts, dict):
            host_info = hosts
        else:
            raise ValueError("No valid host configuration found in API response")
        
        host = host_info.get('host', 'localhost')
        port = host_info.get('port', 27017)
        username = secondary_host_configs.get('userName', '')
        password = secondary_host_configs.get('password', '')
        db_name = config_data.get('dbName', '')
        
        print(f"✓ Extracted connection details:")
        print(f"  Host: {host}")
        print(f"  Port: {port}")
        print(f"  Username: {username}")
        print(f"  Database: {db_name}")
        
        print("\nStep 3: Constructing connection string...")
        
        # Construct connection string
        if username and password:
            if db_name:
                connection_string = f"mongodb://{username}:{password}@{host}:{port}/{db_name}?tls=true"
            else:
                connection_string = f"mongodb://{username}:{password}@{host}:{port}?tls=true"
        else:
            if db_name:
                connection_string = f"mongodb://{host}:{port}/{db_name}"
            else:
                connection_string = f"mongodb://{host}:{port}"
        
        print(f"✓ Connection string: {connection_string}")
        
        print("\nStep 4: Testing MongoDB connection...")
        
        # Test connection
        client = MongoClient(
            connection_string,
            serverSelectionTimeoutMS=5000
        )
        
        # Test the connection
        client.admin.command('ping')
        print("✓ MongoDB connection successful!")
        
        # List databases
        db_list = client.list_database_names()
        print(f"✓ Available databases: {db_list}")
        
        # If specific database is specified, test it
        if db_name:
            db = client[db_name]
            collections = db.list_collection_names()
            print(f"✓ Collections in '{db_name}': {collections[:10]}{'...' if len(collections) > 10 else ''}")
        
        client.close()
        
        print("\nStep 5: Mongo shell commands...")
        print("To connect using mongo shell, run:")
        print(f'mongo "mongodb://{host}:{port}" --username {username} --password {password} --tls')
        print("Then run:")
        print("rs.secondaryOk()")
        if db_name:
            print(f"use {db_name}")
        
        return True
        
    except ServerSelectionTimeoutError:
        print("✗ MongoDB connection timeout - check host, port, and network connectivity")
        return False
    except OperationFailure as e:
        print(f"✗ MongoDB authentication failed: {e}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"✗ API request failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def test_with_sample_response():
    """
    Test with a sample API response structure for demonstration.
    """
    print("Testing with sample API response structure...")
    
    sample_response = {
        "secondaryHostConfigs": {
            "hosts": {
                "host": "sample-mongo-host.example.com",
                "port": 27017
            },
            "userName": "sample_user",
            "password": "sample_password"
        },
        "dbName": "sample_database"
    }
    
    print(f"Sample response: {json.dumps(sample_response, indent=2)}")
    
    # Extract details
    secondary_host_configs = sample_response.get('secondaryHostConfigs', {})
    hosts = secondary_host_configs.get('hosts', {})
    
    host = hosts.get('host', 'localhost')
    port = hosts.get('port', 27017)
    username = secondary_host_configs.get('userName', '')
    password = secondary_host_configs.get('password', '')
    db_name = sample_response.get('dbName', '')
    
    print(f"\nExtracted details:")
    print(f"  Host: {host}")
    print(f"  Port: {port}")
    print(f"  Username: {username}")
    print(f"  Database: {db_name}")
    
    # Generate connection string
    if username and password:
        connection_string = f"mongodb://{username}:{password}@{host}:{port}/{db_name}?tls=true"
    else:
        connection_string = f"mongodb://{host}:{port}/{db_name}"
    
    print(f"\nConnection string: {connection_string}")
    
    print(f"\nMongo shell command:")
    print(f'mongo "mongodb://{host}:{port}" --username {username} --password {password} --tls')
    print("rs.secondaryOk()")
    print(f"use {db_name}")


def main():
    """Main function."""
    print("MongoDB Connection Tester")
    print("=" * 50)
    
    if len(sys.argv) == 1:
        print("No arguments provided. Testing with sample response...")
        test_with_sample_response()
    elif len(sys.argv) >= 3:
        tenant = sys.argv[1]
        dealer_id = sys.argv[2]
        auth_token = sys.argv[3] if len(sys.argv) > 3 else "ved"
        
        print(f"Testing with API: tenant={tenant}, dealer_id={dealer_id}")
        success = test_mongo_connection_from_api(tenant, dealer_id, auth_token)
        
        if success:
            print("\n✓ All tests passed!")
        else:
            print("\n✗ Connection test failed!")
            sys.exit(1)
    else:
        print("Usage:")
        print("  python test_mongo_connection.py                              # Test with sample data")
        print("  python test_mongo_connection.py <tenant> <dealer_id> [token] # Test with API")
        sys.exit(1)


if __name__ == "__main__":
    main()
