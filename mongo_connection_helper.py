#!/usr/bin/env python3
"""
MongoDB Connection Helper Script

This script demonstrates how to extract MongoDB connection details from a curl API response
and connect to MongoDB using the extracted values.

Based on the curl response structure:
- host -> secondaryHostConfigs.hosts.host
- port -> secondaryHostConfigs.hosts.port  
- username -> secondaryHostConfigs.userName
- password -> secondaryHostConfigs.password
- db -> dbName

Connection format:
mongo "mongodb://<HOST>:<PORT>" --username <USER> --password <PASSWORD> --tls
rs.secondaryOk()
use <db>;
"""

import json
import sys
import subprocess
import requests
from typing import Dict, Any, Optional


def extract_mongo_connection_details(api_response: Dict[str, Any]) -> Dict[str, str]:
    """
    Extract MongoDB connection details from API response.
    
    Args:
        api_response: The JSON response from the curl API call
        
    Returns:
        Dictionary containing connection details
    """
    try:
        # Extract values according to the specified structure
        secondary_host_configs = api_response.get('secondaryHostConfigs', {})
        
        # Handle hosts - could be a dict or list
        hosts = secondary_host_configs.get('hosts', {})
        if isinstance(hosts, list) and len(hosts) > 0:
            host_info = hosts[0]  # Use first host
        elif isinstance(hosts, dict):
            host_info = hosts
        else:
            raise ValueError("No valid host configuration found in API response")
        
        host = host_info.get('host', 'localhost')
        port = host_info.get('port', 27017)
        username = secondary_host_configs.get('userName', '')
        password = secondary_host_configs.get('password', '')
        db_name = api_response.get('dbName', '')
        
        return {
            'host': str(host),
            'port': str(port),
            'username': str(username),
            'password': str(password),
            'db_name': str(db_name)
        }
        
    except Exception as e:
        print(f"Error extracting MongoDB connection details: {e}")
        raise


def generate_mongo_shell_commands(connection_details: Dict[str, str]) -> Dict[str, str]:
    """
    Generate MongoDB shell commands based on connection details.
    
    Args:
        connection_details: Dictionary with host, port, username, password, db_name
        
    Returns:
        Dictionary with various command formats
    """
    host = connection_details['host']
    port = connection_details['port']
    username = connection_details['username']
    password = connection_details['password']
    db_name = connection_details['db_name']
    
    # Basic connection command
    mongo_uri = f"mongodb://{host}:{port}"
    
    # Full connection command with authentication
    if username and password:
        mongo_command = f'mongo "{mongo_uri}" --username {username} --password {password} --tls'
    else:
        mongo_command = f'mongo "{mongo_uri}"'
    
    # Python connection string
    if username and password:
        if db_name:
            python_connection_string = f"mongodb://{username}:{password}@{host}:{port}/{db_name}?tls=true"
        else:
            python_connection_string = f"mongodb://{username}:{password}@{host}:{port}?tls=true"
    else:
        if db_name:
            python_connection_string = f"mongodb://{host}:{port}/{db_name}"
        else:
            python_connection_string = f"mongodb://{host}:{port}"
    
    # Complete shell script
    shell_script = f"""#!/bin/bash
# MongoDB Connection Script
echo "Connecting to MongoDB..."
{mongo_command}
"""
    
    # MongoDB shell commands to run after connection
    mongo_shell_commands = []
    mongo_shell_commands.append("rs.secondaryOk()")
    if db_name:
        mongo_shell_commands.append(f"use {db_name}")
    
    return {
        'mongo_command': mongo_command,
        'python_connection_string': python_connection_string,
        'shell_script': shell_script,
        'mongo_shell_commands': mongo_shell_commands,
        'mongo_uri': mongo_uri
    }


def test_api_and_extract_connection(tenant: str, dealer_id: str, auth_token: str = "ved") -> Optional[Dict[str, Any]]:
    """
    Test API connection and extract MongoDB connection details.
    
    Args:
        tenant: Tenant ID
        dealer_id: Dealer ID  
        auth_token: Authentication token
        
    Returns:
        Dictionary with connection details and commands, or None if failed
    """
    api_url = f"http://internal-prod-dms-pvt-1426699881.us-west-1.elb.amazonaws.com/config/v2/read/secondary/MONGO/DSE_DEFAULT/{tenant}/{dealer_id}"
    
    try:
        print(f"Testing API connection...")
        print(f"URL: {api_url}")
        print(f"Auth Token: {auth_token}")
        print("-" * 50)
        
        response = requests.get(
            api_url,
            params={"authToken": auth_token},
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                api_response = response.json()
                print(f"✓ API call successful!")
                print(f"Raw API Response: {json.dumps(api_response, indent=2)}")
                print("-" * 50)
                
                # Handle case where API returns a list
                if isinstance(api_response, list):
                    if len(api_response) > 0:
                        api_response = api_response[0]
                    else:
                        print("✗ API returned empty list")
                        return None
                
                # Extract connection details
                connection_details = extract_mongo_connection_details(api_response)
                commands = generate_mongo_shell_commands(connection_details)
                
                return {
                    'connection_details': connection_details,
                    'commands': commands,
                    'raw_response': api_response
                }
                
            except json.JSONDecodeError as e:
                print(f"✗ Error parsing JSON response: {e}")
                print(f"Response Text: {response.text}")
                return None
        else:
            print(f"✗ API call failed with status {response.status_code}")
            print(f"Error Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ API connection test failed: {e}")
        return None


def print_connection_info(result: Dict[str, Any]):
    """Print formatted connection information."""
    connection_details = result['connection_details']
    commands = result['commands']
    
    print("=" * 60)
    print("MONGODB CONNECTION DETAILS")
    print("=" * 60)
    
    print(f"Host: {connection_details['host']}")
    print(f"Port: {connection_details['port']}")
    print(f"Username: {connection_details['username']}")
    print(f"Password: {'*' * len(connection_details['password']) if connection_details['password'] else '(none)'}")
    print(f"Database: {connection_details['db_name'] or '(none specified)'}")
    
    print("\n" + "=" * 60)
    print("MONGO SHELL CONNECTION")
    print("=" * 60)
    
    print("1. Connect using mongo shell:")
    print(f"   {commands['mongo_command']}")
    
    print("\n2. After connection, run these commands:")
    for cmd in commands['mongo_shell_commands']:
        print(f"   {cmd}")
    
    print("\n" + "=" * 60)
    print("PYTHON CONNECTION STRING")
    print("=" * 60)
    print(f"{commands['python_connection_string']}")
    
    print("\n" + "=" * 60)
    print("SHELL SCRIPT")
    print("=" * 60)
    print(commands['shell_script'])


def main():
    """Main function to test API and extract connection details."""
    if len(sys.argv) < 3:
        print("Usage: python mongo_connection_helper.py <tenant> <dealer_id> [auth_token]")
        print("Example: python mongo_connection_helper.py my_tenant my_dealer_id ved")
        sys.exit(1)
    
    tenant = sys.argv[1]
    dealer_id = sys.argv[2]
    auth_token = sys.argv[3] if len(sys.argv) > 3 else "ved"
    
    print("MongoDB Connection Helper")
    print("=" * 60)
    
    result = test_api_and_extract_connection(tenant, dealer_id, auth_token)
    
    if result:
        print_connection_info(result)
        print("\n✓ Connection details extracted successfully!")
    else:
        print("\n✗ Failed to extract connection details from API")
        sys.exit(1)


if __name__ == "__main__":
    main()
